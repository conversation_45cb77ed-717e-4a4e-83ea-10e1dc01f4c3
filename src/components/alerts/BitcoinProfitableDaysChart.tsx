"use client";

import React, { useMemo } from 'react';
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { useCoinglassData } from '@/lib/state';
import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';
import { FullscreenProvider } from '../comman/FullscreenProvider';
import FullscreenButton from '../comman/FullscreenButton';

interface BitcoinPriceData {
  timestamp: number;
  price: number;
  side: number; // 1 for profitable, 0 for not profitable
}

const BitcoinProfitableDaysChart: React.FC = () => {
  const { data: profitableData, isLoading, error } = useCoinglassData('bitcoin-profitable-days');

  const getBarColor = (percentage: number) => {
    // Color based on profitability percentage
    if (percentage >= 90) return "#22C55E"; // Green for high profitability
    if (percentage >= 70) return "#84CC16"; // Light green
    if (percentage >= 50) return "#EAB308"; // Yellow
    if (percentage >= 30) return "#F97316"; // Orange
    return "#EF4444"; // Red for low profitability
  };
  
  const chartData = useMemo(() => {
    if (!profitableData || !Array.isArray(profitableData)) {
      return [];
    }

    // Calculate profitable days for different holding periods
    const holdingPeriods = [
      { days: 30, label: "1M" },
      { days: 90, label: "3M" },
      { days: 180, label: "6M" },
      { days: 365, label: "1Y" },
      { days: 730, label: "2Y" },
      { days: 1095, label: "3Y" }
    ];

    return holdingPeriods.map((period) => {
      // Calculate profitability for this holding period
      let profitableCount = 0;
      let totalValidPeriods = 0;

      // For each possible starting point, check if holding for 'period.days' would be profitable
      for (let i = 0; i < profitableData.length - period.days; i++) {
        const startItem = profitableData[i] as BitcoinPriceData;
        const endIndex = Math.min(i + period.days, profitableData.length - 1);
        const endItem = profitableData[endIndex] as BitcoinPriceData;
        const startPrice = startItem.price;
        const endPrice = endItem.price;

        if (startPrice > 0 && endPrice > 0) {
          totalValidPeriods++;
          if (endPrice > startPrice) {
            profitableCount++;
          }
        }
      }

      const profitablePercentage = totalValidPeriods > 0 ? (profitableCount / totalValidPeriods) * 100 : 0;

      return {
        days: period.days,
        profitablePercentage,
        totalDays: totalValidPeriods,
        profitableDays: profitableCount,
        label: period.label,
        color: getBarColor(profitablePercentage),
      };
    });
  }, [profitableData]);

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 p-3 border border-gray-600 rounded-lg shadow-lg">
          <p className="text-white font-medium">{`Holding Period: ${label}`}</p>
          <p className="text-green-400 text-sm">
            {`Profitable: ${formatPercentage(data.profitablePercentage)}`}
          </p>
          {data.profitableDays && data.totalDays && (
            <p className="text-gray-300 text-sm">
              {`${data.profitableDays} out of ${data.totalDays} days`}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-red-400 text-lg">Error loading Bitcoin profitable days data</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            {error.message || 'Failed to fetch Bitcoin profitable days data'}
          </p>
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Bitcoin Profitable Days Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The Coinglass API integration is ready. Backend endpoints are being deployed to fetch Bitcoin profitable days data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center">
        <h1 className="text-large font-semibold">Bitcoin Profitable Days</h1>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="Bitcoin Profitable Days">
        <div className="w-full space-y-8">
        <div className="space-y-8">
          <div className="w-full space-y-4">
            <div className="w-full">
              <div style={{ width: "100%", height: 500 }}>
                <ResponsiveContainer width="100%" height={500}>
                  <BarChart
                    data={chartData}
                    barCategoryGap="20%"
                  >
                    <CartesianGrid strokeDasharray="6 6" stroke="#5d5e5f" />
                    <XAxis
                      dataKey="label"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickLine={false}
                      axisLine={false}
                      // angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis
                      orientation="right"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickFormatter={(value) => `${value}%`}
                      axisLine={false}
                      tickLine={false}
                      domain={[0, 100]}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar
                      dataKey="profitablePercentage"
                      radius={[4, 4, 0, 0]}
                      name="Profitable Percentage"
                    >
                      {chartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
            <div className="flex justify-center space-x-6 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <span className="text-gray-300">90%+ Profitable</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-lime-500 rounded"></div>
                <span className="text-gray-300">70-90% Profitable</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                <span className="text-gray-300">50-70% Profitable</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                <span className="text-gray-300">30-50% Profitable</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-red-500 rounded"></div>
                <span className="text-gray-300">Below 30% Profitable</span>
              </div>
            </div>
          </div>
        </div>
        </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  );
};

export default BitcoinProfitableDaysChart;
