"use client";

import React, { useMemo } from 'react';
import {
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  ComposedChart,
} from "recharts";
import { useCoinglassData } from '@/lib/state';
import Loader from '../comman/Loader';
import FullscreenWrapper from '../comman/FullscreenWrapper';
import { FullscreenProvider } from '../comman/FullscreenProvider';
import FullscreenButton from '../comman/FullscreenButton';
import { formatChartDate } from '@/lib/utils';

interface BitcoinPriceData {
  timestamp: number;
  price: number;
  side: number; // 1 for profitable, 0 for not profitable
}

const BitcoinProfitableDaysChart: React.FC = () => {
  const { data: profitableData, isLoading, error } = useCoinglassData('bitcoin-profitable-days');

  const chartData = useMemo(() => {
    if (!profitableData || !Array.isArray(profitableData)) {
      return [];
    }

    // Transform the data for time-series chart
    return profitableData.map((item: BitcoinPriceData) => ({
      timestamp: item.timestamp,
      price: item.price,
      side: item.side,
      time: formatChartDate(new Date(item.timestamp * 1000).toISOString().split('T')[0]),
      formattedDate: new Date(item.timestamp * 1000).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      }),
      formattedPrice: `$${item.price.toLocaleString()}`
    })).sort((a, b) => a.timestamp - b.timestamp);
  }, [profitableData]);

  const formatPrice = (value: number) => {
    if (value >= 1000000) {
      return `$${(value / 1000000).toFixed(1)}M`;
    }
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(1)}K`;
    }
    return `$${value.toLocaleString()}`;
  };

  const formatXAxisTick = (tickItem: string) => {
    // Show fewer ticks to avoid crowding
    return tickItem;
  };

  const CustomTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-gray-800 p-3 border border-gray-600 rounded-lg shadow-lg">
          <p className="text-white font-medium">{data.formattedDate}</p>
          <p className="text-green-400 text-sm">
            {`BTC Price: ${data.formattedPrice}`}
          </p>
          <p className="text-gray-300 text-sm">
            {data.side === 1 ? 'Profitable Day' : 'Non-Profitable Day'}
          </p>
        </div>
      );
    }
    return null;
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center" style={{ height: 500 }}>
          <Loader />
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-red-400 text-lg">Error loading Bitcoin profitable days data</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            {error.message || 'Failed to fetch Bitcoin profitable days data'}
          </p>
        </div>
      </div>
    );
  }

  if (!chartData || chartData.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-center flex-col space-y-4" style={{ height: 500 }}>
          <p className="text-white text-lg">Bitcoin Profitable Days Data Coming Soon</p>
          <p className="text-gray-400 text-sm text-center max-w-md">
            The Coinglass API integration is ready. Backend endpoints are being deployed to fetch Bitcoin profitable days data.
          </p>
        </div>
      </div>
    );
  }

  return (
    <FullscreenProvider>
      <div className="flex justify-between items-center">
        <h1 className="text-large font-semibold">Bitcoin Profitable Days</h1>
        <FullscreenButton />
      </div>
      <FullscreenWrapper title="Bitcoin Profitable Days">
        <div className="w-full space-y-8">
        <div className="space-y-8">
          <div className="w-full space-y-4">
            <div className="w-full">
              <div style={{ width: "100%", height: 500 }}>
                <ResponsiveContainer width="100%" height={500}>
                  <ComposedChart data={chartData}>
                    <CartesianGrid stroke="#5d5e5f" strokeDasharray="6 6" />
                    <XAxis
                      dataKey="time"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickFormatter={formatXAxisTick}
                      axisLine={false}
                      tickLine={false}
                      minTickGap={40}
                    />
                    <YAxis
                      orientation="right"
                      tick={{ fill: "#fff", fontSize: 10 }}
                      tickFormatter={formatPrice}
                      axisLine={false}
                      tickLine={false}
                      domain={["dataMin", "dataMax"]}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Area
                      type="monotone"
                      dataKey="price"
                      stroke="none"
                      fill="rgba(187, 217, 85, 0.2)"
                      tooltipType="none"
                    />
                    <Line
                      type="monotone"
                      dataKey="price"
                      stroke="#bbd955"
                      strokeWidth={2}
                      dot={false}
                      activeDot={{ r: 4, fill: "#bbd955" }}
                      name="Bitcoin Price"
                    />
                  </ComposedChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        </div>
        </div>
      </FullscreenWrapper>
    </FullscreenProvider>
  );
};

export default BitcoinProfitableDaysChart;
